import datetime
import os
import subprocess
import platform
from langchain_core.messages import HumanMessage
from src.utilities.util_functions import join_paths


def create_script_execution_env(work_dir: str, silent: bool = True) -> str:
    """Creates a virtual environment for executing code generated by the Clean Coder.

    Args:
        work_dir: Working directory path
        silent: If True, suppresses environment setup output
    """
    work_dir = os.path.abspath(work_dir)
    env_path = join_paths(work_dir, "env")
    
    is_windows = platform.system() == "Windows"
    bin_dir = "Scripts" if is_windows else "bin"
    python_exe = "python.exe" if is_windows else "python"
    pip_exe = "pip.exe" if is_windows else "pip"
    
    if not os.path.exists(env_path):
        import venv
        venv.create(env_path, with_pip=True)
        stdout = subprocess.DEVNULL if silent else None
        stderr = subprocess.DEVNULL if silent else None
        
        pip_path = join_paths(env_path, bin_dir, pip_exe)
        
        try:
            subprocess.run([pip_path, "install", "-U", "pip"],
                        check=True, stdout=stdout, stderr=stderr)
        except Exception:
            pass
    
    python_path = join_paths(env_path, bin_dir, python_exe)
    return python_path


def run_script_in_env(work_dir: str, execute_file_name: str, silent_setup: bool = True) -> tuple[str, str]:
    """Runs generated script in a virtual environment.

    Args:
        work_dir: Working directory path
        execute_file_name: Name of the file to execute
        silent_setup: If True, suppresses environment setup output
    """
    work_dir = os.path.abspath(work_dir)
    script_path = join_paths(work_dir, execute_file_name)
    python_path = create_script_execution_env(work_dir, silent=silent_setup)
    req_file = join_paths(work_dir, "requirements.txt")
    
    if os.path.exists(req_file):
        is_windows = platform.system() == "Windows"
        bin_dir = "Scripts" if is_windows else "bin"
        pip_exe = "pip.exe" if is_windows else "pip"
        
        pip_path = join_paths(os.path.dirname(os.path.dirname(python_path)), bin_dir, pip_exe)
        
        stdout = subprocess.DEVNULL if silent_setup else None
        stderr = subprocess.DEVNULL if silent_setup else None
        try:
            subprocess.run([pip_path, "install", "-r", req_file],
                        check=True, stdout=stdout, stderr=stderr)
        except Exception:
            pass
    try:
        result = subprocess.run([python_path, script_path], capture_output=True, text=True, check=True)
        return result.stdout, result.stderr
    except subprocess.CalledProcessError as e:
        return e.stdout, e.stderr


def format_log_message(stdout: str = "", stderr: str = "", is_error: bool = False, error_msg: str = "") -> str:
    """Format logs (stdout, stderr) for insertion into the Clean Coder context."""
    message = "\n[SCRIPT EXECUTION {}]\n".format("ERROR" if is_error else "INFO")
    if error_msg:
        message += f"Error: {error_msg}\n"
    if stdout:
        message += f"STDOUT:\n{stdout}\n"
    if stderr:
        message += f"STDERR:\n{stderr}\n"

    return message


def logs_from_running_script(work_dir: str, execute_file_name: str, silent_setup: bool = True) -> str:
    """Get logs from running script execution.

    Args:
        work_dir: Working directory path
        execute_file_name: Name of the file to execute
        silent_setup: If True, suppresses environment setup output
    """
    try:
        stdout, stderr = run_script_in_env(work_dir, execute_file_name, silent_setup=silent_setup)
        return format_log_message(stdout=stdout, stderr=stderr)
    except subprocess.CalledProcessError as e:
        stdout = e.stdout if hasattr(e, "stdout") else ""
        stderr = e.stderr if hasattr(e, "stderr") else ""
        message = format_log_message(
            stdout=stdout,
            stderr=stderr,
            is_error=True,
            error_msg=f"Script execution failed with return code {e.returncode}",
        )
        return message
    except Exception as e:
        return format_log_message(is_error=True, error_msg=f"Error: {str(e)}")
