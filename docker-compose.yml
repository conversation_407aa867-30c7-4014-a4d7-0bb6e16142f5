services:
  manager:
    build:
      context: .
      dockerfile: non_src/docker/Dockerfile
    container_name: manager
    environment:
      - WORK_DIR=/work_dir
      - OPENAI_API_KEY=${OPENAI_API_KEY}
      - ANTHROPIC_API_KEY=${ANTHROPIC_API_KEY}
      - TODOIST_API_KEY=${TODOIST_API_KEY}
      - TODOIST_PROJECT_ID=${TODOIST_PROJECT_ID}
      - LOG_FILE=${LOG_FILE:-}
    volumes:
      - .:/Clean_Coder
      - ${WORK_DIR:-.}:/work_dir
    # Uncomment (linux) or adjust (other systems) the following line to use microphone
    # devices:
    #   - "/dev/snd:/dev/snd"
    command: python manager.py

  single_task_coder:
    build:
      context: .
      dockerfile: non_src/docker/Dockerfile
    container_name: single_task_coder
    environment:
      - WORK_DIR=/work_dir
      - OPENAI_API_KEY=${OPENAI_API_KEY}
      - ANTHROPIC_API_KEY=${ANTHROPIC_API_KEY}
      - LOG_FILE=${LOG_FILE:-}
    volumes:
      - .:/Clean_Coder
      - ${WORK_DIR:-.}:/work_dir
    # Uncomment (linux) or adjust (other systems) the following line to use microphone
    # devices:
    #   - "/dev/snd:/dev/snd"
    command: python single_task_coder.py