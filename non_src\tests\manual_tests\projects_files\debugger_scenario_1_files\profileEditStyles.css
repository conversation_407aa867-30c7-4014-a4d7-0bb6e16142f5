form {
  display: flex;
  flex-direction: column;
  gap: 1rem;
  text-align: left;
  max-width: 500px;
  width: 100%;
  padding: 2rem;
  background-color: #28a745;
  border-radius: 10px;
  margin: 2rem auto;
  box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
}

form div {
  display: flex;
  flex-direction: column;
  margin-bottom: 1rem;
}

form label {
  font-weight: bold;
  margin-bottom: 0.5rem;
  color: #333;
}

form input,
form select {
  padding: 0.75rem;
  border: 1px solid #ccc;
  border-radius: 6px;
  width: 100%;
  font-size: 1rem;
}

form input:focus,
form select:focus {
  border-color: #007bff;
  outline: none;
  box-shadow: 0 0 0 3px rgba(0, 123, 255, 0.25);
}

form button {
  padding: 0.75rem;
  background-color: #4c06b5;
  color: white;
  border: none;
  border-radius: 6px;
  cursor: pointer;
  font-size: 1rem;
  transition: background-color 0.3s;
}

form button:hover {
  background-color: #218838;
}

h1 {
  text-align: center;
  margin-bottom: 2rem;
  font-size: 2rem;
  color: #333;
}

.checkbox-container {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  margin-top: 0.5rem;
}

.checkbox-container input {
  margin: 0;
}
