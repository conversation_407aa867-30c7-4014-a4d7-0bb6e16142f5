#!/usr/bin/env python3
"""
Clean Coder AI - Interactive REPL Mode
Similar to Gemini CLI, <PERSON>, and Augment Code

Usage:
    python clean_coder_interactive.py
"""

import sys
import os

# Add the project root and src directory to Python path
project_root = os.path.abspath(os.path.dirname(__file__))
src_path = os.path.join(project_root, "src")
sys.path.insert(0, project_root)
sys.path.insert(0, src_path)

from dotenv import load_dotenv, find_dotenv
from src.utilities.interactive_repl import CleanCoderREPL
from src.utilities.set_up_dotenv import set_up_env_manager


def main():
    """Main entry point for interactive mode"""
    
    # Set up environment if needed
    if not find_dotenv():
        print("Setting up Clean Coder AI for the first time...")
        set_up_env_manager()
    
    # Load environment variables
    load_dotenv(find_dotenv())
    
    # Get work directory
    work_dir = os.getenv("WORK_DIR")
    if not work_dir:
        work_dir = os.getcwd()
        print(f"Using current directory as work directory: {work_dir}")
    
    # Ensure work directory exists
    if not os.path.exists(work_dir):
        print(f"Work directory does not exist: {work_dir}")
        sys.exit(1)
    
    # Create and run REPL
    try:
        repl = CleanCoderREPL(work_dir)
        repl.run()
    except KeyboardInterrupt:
        print("\n👋 Goodbye!")
    except Exception as e:
        print(f"❌ Error starting Clean Coder AI: {e}")
        sys.exit(1)


if __name__ == "__main__":
    main()
