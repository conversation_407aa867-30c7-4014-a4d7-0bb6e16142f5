"""
Interactive REPL interface for Clean Coder AI
Similar to Gemini CLI, <PERSON>, and Augment Code
"""

import os
import sys
import json
from datetime import datetime
from typing import Dict, List, Optional
from termcolor import colored
import questionary
from dotenv import load_dotenv

from src.utilities.util_functions import join_paths
from src.utilities.local_task_storage import LocalTaskStorage
from src.utilities.start_work_functions import print_ascii_logo
from src.utilities.manager_utils import get_manager_messages, setup_local_project_if_needed
from src.utilities.llms import init_llms_medium_intelligence


class CleanCoderREPL:
    def __init__(self, work_dir: str):
        self.work_dir = work_dir
        self.local_storage = LocalTaskStorage(work_dir)
        self.session_history = []
        self.current_mode = "chat"  # chat, manager, coder
        self.running = True

        # Load environment
        load_dotenv()

        # Initialize LLMs
        self.llms = init_llms_medium_intelligence()

        # Setup local project if needed
        setup_local_project_if_needed()

        # Initialize session
        self.init_session()
    
    def init_session(self):
        """Initialize the REPL session"""
        print_ascii_logo()
        print(colored("\n🤖 Clean Coder AI - Interactive Mode", "cyan", attrs=["bold"]))
        print(colored("Your AI-powered coding assistant with local storage", "blue"))
        print(colored("=" * 60, "blue"))

        # Check if Gemini is configured
        if not os.getenv("GOOGLE_API_KEY"):
            print(colored("⚠️  Warning: GOOGLE_API_KEY not found. Please configure it in .env file.", "yellow"))
        else:
            print(colored("✅ Gemini AI is ready!", "green"))

        # Show current project info
        project_id = self.local_storage.get_current_project_id()
        if project_id:
            projects = self.local_storage.get_projects()
            current_project = next((p for p in projects if p.id == project_id), None)
            if current_project:
                print(colored(f"📁 Current Project: {current_project.name}", "magenta"))

        print(colored("\n💡 Quick Start:", "yellow", attrs=["bold"]))
        print(colored("  • Type '/help' for all commands", "white"))
        print(colored("  • Type '/mode' to switch between chat/manager/coder modes", "white"))
        print(colored("  • Just start typing to chat with AI!", "white"))
        print(colored("=" * 60, "blue"))
    
    def print_prompt(self):
        """Print the interactive prompt"""
        mode_color = {
            "chat": "cyan",
            "manager": "magenta", 
            "coder": "yellow"
        }
        
        prompt = colored(f"clean-coder:{self.current_mode}", mode_color.get(self.current_mode, "white"))
        prompt += colored(" > ", "white", attrs=["bold"])
        return prompt
    
    def handle_command(self, command: str) -> bool:
        """Handle special commands. Returns True if command was handled."""
        command = command.strip().lower()
        
        if command == "/help":
            self.show_help()
            return True
        elif command == "/exit" or command == "/quit":
            self.running = False
            print(colored("👋 Goodbye!", "green"))
            return True
        elif command == "/clear":
            os.system('cls' if os.name == 'nt' else 'clear')
            self.init_session()
            return True
        elif command == "/mode":
            self.change_mode()
            return True
        elif command == "/status":
            self.show_status()
            return True
        elif command == "/history":
            self.show_history()
            return True
        elif command == "/new-project":
            self.create_new_project()
            return True
        elif command == "/tasks":
            self.show_tasks()
            return True
        elif command == "/projects":
            self.show_projects()
            return True
        elif command == "/create-task":
            self.create_task()
            return True
        elif command == "/complete-task":
            self.complete_task()
            return True
        elif command == "/version":
            self.show_version()
            return True
        elif command.startswith("/mode "):
            mode = command.split(" ", 1)[1]
            if mode in ["chat", "manager", "coder"]:
                self.current_mode = mode
                print(colored(f"✅ Switched to {mode} mode", "green"))
            else:
                print(colored("❌ Invalid mode. Use: chat, manager, or coder", "red"))
            return True
        
        return False
    
    def show_help(self):
        """Show help information"""
        help_text = """
🤖 Clean Coder AI - Interactive Commands

📋 General Commands:
  /help          - Show this help message
  /exit, /quit   - Exit the application
  /clear         - Clear screen and restart
  /status        - Show current status
  /history       - Show session history

🔧 Mode Commands:
  /mode          - Interactive mode selection
  /mode chat     - Switch to chat mode (general AI assistance)
  /mode manager  - Switch to manager mode (project planning)
  /mode coder    - Switch to coder mode (code execution)

📁 Project Commands:
  /new-project   - Create a new project
  /projects      - Show all projects
  /tasks         - Show current tasks
  /create-task   - Create a new task
  /complete-task - Mark a task as complete
  /version       - Show version information
  
💡 Usage Examples:
  - Just type your message to chat with AI
  - "Create a todo app in HTML" (in any mode)
  - "Plan my project structure" (works best in manager mode)
  - "Fix this bug in my code" (works best in coder mode)

🎨 Current Mode: """ + colored(self.current_mode, "cyan", attrs=["bold"])
        
        print(colored(help_text, "white"))
    
    def change_mode(self):
        """Interactive mode selection"""
        modes = [
            "chat - General AI assistance and conversation",
            "manager - Project planning and task management", 
            "coder - Code execution and development tasks"
        ]
        
        choice = questionary.select(
            "Select mode:",
            choices=modes,
            style=questionary.Style([
                ('question', 'fg:#ff0066 bold'),
                ('answer', 'fg:#44ff00 bold'),
                ('pointer', 'fg:#ff0066 bold'),
                ('highlighted', 'fg:#ff0066 bold'),
                ('selected', 'fg:#cc5454'),
                ('separator', 'fg:#cc5454'),
                ('instruction', ''),
                ('text', ''),
                ('disabled', 'fg:#858585 italic')
            ])
        ).ask()
        
        if choice:
            self.current_mode = choice.split(" - ")[0]
            print(colored(f"✅ Switched to {self.current_mode} mode", "green"))
    
    def show_status(self):
        """Show current status"""
        project_id = self.local_storage.get_current_project_id()
        projects = self.local_storage.get_projects()
        tasks = self.local_storage.get_tasks() if project_id else []
        
        status_info = f"""
📊 Clean Coder AI Status

🔧 Current Mode: {colored(self.current_mode, "cyan", attrs=["bold"])}
📁 Work Directory: {colored(self.work_dir, "blue")}
🆔 Current Project: {colored(project_id or "None", "yellow")}
📋 Total Projects: {colored(str(len(projects)), "green")}
✅ Total Tasks: {colored(str(len(tasks)), "green")}
🤖 AI Model: {colored("Gemini Flash 2.0/2.5", "magenta")}
"""
        print(status_info)
    
    def show_history(self):
        """Show session history"""
        if not self.session_history:
            print(colored("📝 No history yet in this session", "yellow"))
            return
        
        print(colored("\n📝 Session History:", "cyan", attrs=["bold"]))
        print(colored("-" * 50, "blue"))
        
        for i, entry in enumerate(self.session_history[-10:], 1):  # Show last 10
            timestamp = entry.get("timestamp", "")
            user_input = entry.get("input", "")
            mode = entry.get("mode", "")
            
            print(colored(f"{i}. [{timestamp}] [{mode}]", "blue"))
            print(colored(f"   User: {user_input}", "white"))
            print()
    
    def create_new_project(self):
        """Create a new project interactively"""
        project_name = questionary.text(
            "Enter project name:",
            style=questionary.Style([
                ('question', 'fg:#ff0066 bold'),
                ('answer', 'fg:#44ff00 bold')
            ])
        ).ask()
        
        if project_name:
            project_id = self.local_storage.create_project(project_name)
            print(colored(f"✅ Created project: {project_name} (ID: {project_id})", "green"))
        else:
            print(colored("❌ Project creation cancelled", "red"))
    
    def show_tasks(self):
        """Show current tasks"""
        tasks = self.local_storage.get_tasks()

        if not tasks:
            print(colored("📝 No tasks found", "yellow"))
            return

        print(colored("\n📋 Current Tasks:", "cyan", attrs=["bold"]))
        print(colored("-" * 50, "blue"))

        for i, task in enumerate(tasks, 1):
            status = "✅" if task.completed else "⏳"
            print(colored(f"{i}. {status} {task.content}", "white"))
            if task.description:
                print(colored(f"   📝 {task.description}", "blue"))
            print()

    def show_projects(self):
        """Show all projects"""
        projects = self.local_storage.get_projects()
        current_project_id = self.local_storage.get_current_project_id()

        if not projects:
            print(colored("📁 No projects found", "yellow"))
            return

        print(colored("\n📁 All Projects:", "cyan", attrs=["bold"]))
        print(colored("-" * 50, "blue"))

        for i, project in enumerate(projects, 1):
            current = "👉 " if project.id == current_project_id else "   "
            print(colored(f"{i}. {current}{project.name}", "white"))
            print(colored(f"   🆔 ID: {project.id}", "blue"))
            print()

    def create_task(self):
        """Create a new task interactively"""
        task_content = questionary.text(
            "Enter task description:",
            style=questionary.Style([
                ('question', 'fg:#ff0066 bold'),
                ('answer', 'fg:#44ff00 bold')
            ])
        ).ask()

        if task_content:
            task_description = questionary.text(
                "Enter task details (optional):",
                style=questionary.Style([
                    ('question', 'fg:#ff0066 bold'),
                    ('answer', 'fg:#44ff00 bold')
                ])
            ).ask() or ""

            task_id = self.local_storage.create_task(task_content, task_description)
            print(colored(f"✅ Created task: {task_content} (ID: {task_id})", "green"))
        else:
            print(colored("❌ Task creation cancelled", "red"))

    def complete_task(self):
        """Mark a task as complete"""
        tasks = self.local_storage.get_tasks()

        if not tasks:
            print(colored("📝 No tasks found", "yellow"))
            return

        # Show tasks with numbers
        incomplete_tasks = [task for task in tasks if not task.completed]

        if not incomplete_tasks:
            print(colored("✅ All tasks are already completed!", "green"))
            return

        task_choices = [f"{i+1}. {task.content}" for i, task in enumerate(incomplete_tasks)]

        choice = questionary.select(
            "Select task to complete:",
            choices=task_choices,
            style=questionary.Style([
                ('question', 'fg:#ff0066 bold'),
                ('answer', 'fg:#44ff00 bold'),
                ('pointer', 'fg:#ff0066 bold'),
                ('highlighted', 'fg:#ff0066 bold'),
                ('selected', 'fg:#cc5454'),
            ])
        ).ask()

        if choice:
            task_index = int(choice.split(".")[0]) - 1
            selected_task = incomplete_tasks[task_index]
            self.local_storage.complete_task(selected_task.id)
            print(colored(f"✅ Completed task: {selected_task.content}", "green"))
        else:
            print(colored("❌ Task completion cancelled", "red"))

    def show_version(self):
        """Show version information"""
        version_info = f"""
🤖 Clean Coder AI - Interactive Mode

📦 Version: 1.0.0
🚀 Mode: Interactive REPL
🤖 AI Model: Gemini Flash 2.0/2.5
🐍 Python: {sys.version.split()[0]}
📁 Work Dir: {self.work_dir}

🔗 GitHub: https://github.com/clean-coder-ai
📚 Docs: https://clean-coder.dev
"""
        print(colored(version_info, "white"))
    
    def add_to_history(self, user_input: str):
        """Add entry to session history"""
        entry = {
            "timestamp": datetime.now().strftime("%H:%M:%S"),
            "input": user_input,
            "mode": self.current_mode
        }
        self.session_history.append(entry)
    
    def process_input(self, user_input: str) -> str:
        """Process user input based on current mode"""
        # This will be implemented to call appropriate agents
        # For now, return a placeholder response
        
        if self.current_mode == "manager":
            return self.process_manager_input(user_input)
        elif self.current_mode == "coder":
            return self.process_coder_input(user_input)
        else:  # chat mode
            return self.process_chat_input(user_input)
    
    def process_manager_input(self, user_input: str) -> str:
        """Process input in manager mode"""
        try:
            # Simple manager response using LLM directly
            if self.llms:
                manager_prompt = f"""You are a project manager AI assistant. Help the user with project planning and management.

User request: {user_input}

Please provide helpful project management advice, break down tasks, or help with planning."""

                from langchain_core.messages import HumanMessage
                messages = [HumanMessage(content=manager_prompt)]
                response = self.llms[0].invoke(messages)
                return f"🎯 Manager: {response.content}"
            else:
                return "❌ No LLM available. Please check your API configuration."

        except Exception as e:
            return f"❌ Manager Error: {str(e)}"

    def process_coder_input(self, user_input: str) -> str:
        """Process input in coder mode"""
        try:
            # Simple coder response for now
            if self.llms:
                from langchain_core.messages import HumanMessage
                coder_prompt = f"""You are an expert coding assistant. Help the user with programming tasks, debugging, code review, and implementation.

User request: {user_input}

Please provide detailed coding assistance, examples, and explanations."""

                messages = [HumanMessage(content=coder_prompt)]
                response = self.llms[0].invoke(messages)
                return f"💻 Coder: {response.content}"
            else:
                return "❌ No LLM available. Please check your API configuration."

        except Exception as e:
            return f"❌ Coder Error: {str(e)}"

    def process_chat_input(self, user_input: str) -> str:
        """Process input in chat mode"""
        try:
            # General chat response
            if self.llms:
                from langchain_core.messages import HumanMessage
                messages = [HumanMessage(content=user_input)]
                response = self.llms[0].invoke(messages)
                return f"💬 Assistant: {response.content}"
            else:
                return "❌ No LLM available. Please check your API configuration."

        except Exception as e:
            return f"❌ Chat Error: {str(e)}"
    
    def run(self):
        """Main REPL loop"""
        try:
            while self.running:
                try:
                    # Get user input
                    user_input = input(self.print_prompt()).strip()
                    
                    # Skip empty input
                    if not user_input:
                        continue
                    
                    # Handle commands
                    if user_input.startswith('/'):
                        if self.handle_command(user_input):
                            continue
                    
                    # Add to history
                    self.add_to_history(user_input)
                    
                    # Process input
                    print(colored("🤔 Thinking...", "yellow", attrs=["blink"]))
                    response = self.process_input(user_input)

                    # Format response with nice borders
                    print(colored("─" * 60, "blue"))
                    print(colored(response, "white"))
                    print(colored("─" * 60, "blue"))
                    print()
                    
                except KeyboardInterrupt:
                    print(colored("\n\n⚠️  Use /exit to quit properly", "yellow"))
                    continue
                except EOFError:
                    break
                    
        except Exception as e:
            print(colored(f"❌ Error: {str(e)}", "red"))
        finally:
            print(colored("\n👋 Clean Coder AI session ended", "green"))
