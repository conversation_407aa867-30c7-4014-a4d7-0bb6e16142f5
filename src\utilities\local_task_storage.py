"""
Local task storage system to replace Todoist functionality.
Uses JSON files for storing tasks, epics, and project information.
"""

import json
import os
import uuid
from datetime import datetime
from typing import List, Dict, Optional
from src.utilities.util_functions import join_paths


class LocalTask:
    def __init__(self, id: str, content: str, description: str = "", section_id: str = None, 
                 completed: bool = False, created_at: str = None):
        self.id = id
        self.content = content
        self.description = description
        self.section_id = section_id
        self.completed = completed
        self.created_at = created_at or datetime.now().isoformat()


class LocalSection:
    def __init__(self, id: str, name: str, project_id: str):
        self.id = id
        self.name = name
        self.project_id = project_id


class LocalProject:
    def __init__(self, id: str, name: str):
        self.id = id
        self.name = name


class LocalTaskStorage:
    def __init__(self, work_dir: str):
        self.work_dir = work_dir
        self.storage_dir = join_paths(work_dir, ".clean_coder", "local_storage")
        self.tasks_file = join_paths(self.storage_dir, "tasks.json")
        self.sections_file = join_paths(self.storage_dir, "sections.json")
        self.projects_file = join_paths(self.storage_dir, "projects.json")
        self.project_id_file = join_paths(work_dir, ".clean_coder", "project_id.txt")
        
        # Ensure storage directory exists
        os.makedirs(self.storage_dir, exist_ok=True)
        
        # Initialize files if they don't exist
        self._init_storage_files()
    
    def _init_storage_files(self):
        """Initialize storage files with empty data if they don't exist."""
        for file_path in [self.tasks_file, self.sections_file, self.projects_file]:
            if not os.path.exists(file_path):
                with open(file_path, 'w') as f:
                    json.dump([], f)
    
    def _load_json(self, file_path: str) -> List[Dict]:
        """Load JSON data from file."""
        try:
            with open(file_path, 'r') as f:
                return json.load(f)
        except (FileNotFoundError, json.JSONDecodeError):
            return []
    
    def _save_json(self, file_path: str, data: List[Dict]):
        """Save JSON data to file."""
        with open(file_path, 'w') as f:
            json.dump(data, f, indent=2)
    
    def get_current_project_id(self) -> Optional[str]:
        """Get the current project ID."""
        try:
            with open(self.project_id_file, 'r') as f:
                return f.read().strip()
        except FileNotFoundError:
            return None
    
    def set_current_project_id(self, project_id: str):
        """Set the current project ID."""
        with open(self.project_id_file, 'w') as f:
            f.write(project_id)
    
    def create_project(self, name: str) -> str:
        """Create a new project and return its ID."""
        project_id = str(uuid.uuid4())
        projects = self._load_json(self.projects_file)
        
        project_data = {
            "id": project_id,
            "name": name,
            "created_at": datetime.now().isoformat()
        }
        
        projects.append(project_data)
        self._save_json(self.projects_file, projects)
        self.set_current_project_id(project_id)
        
        return project_id
    
    def get_projects(self) -> List[LocalProject]:
        """Get all projects."""
        projects_data = self._load_json(self.projects_file)
        return [LocalProject(p["id"], p["name"]) for p in projects_data]
    
    def get_sections(self, project_id: str = None) -> List[LocalSection]:
        """Get sections (epics) for a project."""
        if not project_id:
            project_id = self.get_current_project_id()
        
        sections_data = self._load_json(self.sections_file)
        filtered_sections = [s for s in sections_data if s.get("project_id") == project_id]
        return [LocalSection(s["id"], s["name"], s["project_id"]) for s in filtered_sections]
    
    def create_section(self, name: str, project_id: str = None) -> str:
        """Create a new section (epic) and return its ID."""
        if not project_id:
            project_id = self.get_current_project_id()
        
        section_id = str(uuid.uuid4())
        sections = self._load_json(self.sections_file)
        
        section_data = {
            "id": section_id,
            "name": name,
            "project_id": project_id,
            "created_at": datetime.now().isoformat()
        }
        
        sections.append(section_data)
        self._save_json(self.sections_file, sections)
        
        return section_id
    
    def get_tasks(self, project_id: str = None) -> List[LocalTask]:
        """Get tasks for a project."""
        if not project_id:
            project_id = self.get_current_project_id()
        
        tasks_data = self._load_json(self.tasks_file)
        # Filter tasks by checking if their section belongs to the project
        sections = self.get_sections(project_id)
        section_ids = [s.id for s in sections]
        
        filtered_tasks = [t for t in tasks_data if t.get("section_id") in section_ids or 
                         (t.get("section_id") is None and t.get("project_id") == project_id)]
        
        return [LocalTask(
            t["id"], t["content"], t.get("description", ""), 
            t.get("section_id"), t.get("completed", False), t.get("created_at")
        ) for t in filtered_tasks]
    
    def create_task(self, content: str, description: str = "", section_id: str = None, 
                   project_id: str = None) -> str:
        """Create a new task and return its ID."""
        if not project_id:
            project_id = self.get_current_project_id()
        
        task_id = str(uuid.uuid4())
        tasks = self._load_json(self.tasks_file)
        
        task_data = {
            "id": task_id,
            "content": content,
            "description": description,
            "section_id": section_id,
            "project_id": project_id,
            "completed": False,
            "created_at": datetime.now().isoformat()
        }
        
        tasks.append(task_data)
        self._save_json(self.tasks_file, tasks)
        
        return task_id
    
    def move_task(self, task_id: str, section_id: str):
        """Move a task to a different section."""
        tasks = self._load_json(self.tasks_file)
        
        for task in tasks:
            if task["id"] == task_id:
                task["section_id"] = section_id
                break
        
        self._save_json(self.tasks_file, tasks)
    
    def complete_task(self, task_id: str):
        """Mark a task as completed."""
        tasks = self._load_json(self.tasks_file)
        
        for task in tasks:
            if task["id"] == task_id:
                task["completed"] = True
                task["completed_at"] = datetime.now().isoformat()
                break
        
        self._save_json(self.tasks_file, tasks)
    
    def delete_task(self, task_id: str):
        """Delete a task."""
        tasks = self._load_json(self.tasks_file)
        tasks = [t for t in tasks if t["id"] != task_id]
        self._save_json(self.tasks_file, tasks)
